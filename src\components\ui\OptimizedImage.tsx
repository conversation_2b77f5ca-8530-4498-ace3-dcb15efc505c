'use client';

import { browserSupports } from '@/utils/browserCompat';
import Image from 'next/image';
import { useState } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  className?: string;
  priority?: boolean;
  sizes?: string;
  quality?: number;
  loading?: 'eager' | 'lazy';
  fetchPriority?: 'high' | 'low' | 'auto';
  onLoad?: () => void;
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  sizes = '100vw',
  quality = 75,
  loading,
  fetchPriority,
  onLoad,
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);

  const handleLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  const imgFetchPriority = browserSupports.fetchPriority
    ? (fetchPriority || (priority ? 'high' : 'auto'))
    : undefined;

  const imgLoading = loading || (priority ? 'eager' : 'lazy');
  const imageClassName = `${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`;

  return (
    <div className={`relative ${className}`} style={{ aspectRatio: `${width}/${height}` }}>
      {!isLoaded && !priority && (
        <div
          className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"
          style={{ aspectRatio: `${width}/${height}` }}
        />
      )}

      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={imageClassName}
        priority={priority}
        sizes={sizes}
        quality={quality}
        loading={imgLoading}
        onLoad={handleLoad}
        fetchPriority={imgFetchPriority}
      />
    </div>
  );
}
