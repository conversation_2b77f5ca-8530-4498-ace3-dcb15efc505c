'use client';

import { useDarkMode, useThemeColor } from '@/stores/themeStore';
import { useUserPreferences } from '@/stores/userPreferencesStore';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { useEffect, useState } from 'react';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  component: React.ReactNode;
}

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Payday Pilot!',
    description: 'Let&apos;s set up your personal finance dashboard.',
    component: <WelcomeStep />
  },
  {
    id: 'dashboard',
    title: 'Customize Your Dashboard',
    description: 'Choose the widgets that matter most to you.',
    component: <DashboardCustomizationStep />
  },
  {
    id: 'notifications',
    title: 'Stay Updated',
    description: 'How would you like to be reminded about upcoming bills?',
    component: <NotificationPreferencesStep />
  },
  {
    id: 'theme',
    title: 'Make It Yours',
    description: 'Choose your preferred appearance theme.',
    component: <ThemePreferencesStep />
  },
  {
    id: 'complete',
    title: 'All Set!',
    description: 'Your personalized dashboard is ready.',
    component: <CompletionStep />
  }
];

export function OnboardingFlow() {
  const {
    onboarding,
    advanceOnboarding,
    completeOnboarding
  } = useUserPreferences();

  const [currentStepIndex, setCurrentStepIndex] = useState<number>(onboarding.currentStep);
  const [isVisible, setIsVisible] = useState<boolean>(!onboarding.completed);
  const currentStep = ONBOARDING_STEPS[currentStepIndex];

  useEffect(() => {
    // If onboarding is already completed, don't show the flow
    if (onboarding.completed) {
      setIsVisible(false);
    }
  }, [onboarding.completed]);

  const handleNext = () => {
    if (currentStepIndex < ONBOARDING_STEPS.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
      advanceOnboarding();
    } else {
      completeOnboarding();
      setIsVisible(false);
    }
  };

  const handleSkip = () => {
    completeOnboarding();
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <div className="w-full max-w-xl">
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
        >
          {/* Progress indicator */}
          <div className="w-full h-1 bg-gray-100 dark:bg-gray-700">
            <motion.div
              className="h-full bg-blue-500"
              initial={{ width: `${(currentStepIndex / (ONBOARDING_STEPS.length - 1)) * 100}%` }}
              animate={{ width: `${(currentStepIndex / (ONBOARDING_STEPS.length - 1)) * 100}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>

          <div className="p-6">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{currentStep.title}</h2>
              <p className="text-gray-600 dark:text-gray-400 mt-2">{currentStep.description}</p>
            </div>

            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="min-h-[260px]"
              >
                {currentStep.component}
              </motion.div>
            </AnimatePresence>
          </div>

          <div className="flex justify-between items-center px-6 py-4 bg-gray-50 dark:bg-gray-750">
            <button
              onClick={handleSkip}
              className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
            >
              {currentStepIndex === ONBOARDING_STEPS.length - 1 ? 'Close' : 'Skip for now'}
            </button>

            <button
              onClick={handleNext}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition-colors"
            >
              {currentStepIndex === ONBOARDING_STEPS.length - 1 ? 'Get Started' : 'Continue'}
            </button>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
}

function WelcomeStep() {
  return (
    <div className="flex flex-col items-center text-center">
      <div className="w-32 h-32 mb-4 relative">
        <Image
          src="/images/logo.png"
          alt="Payday Pilot Logo"
          width={128}
          height={128}
        />
      </div>
      <p className="text-lg text-gray-700 dark:text-gray-300">
        Payday Pilot helps you manage your finances with smart bill tracking and financial insights.
      </p>
      <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
        We&apos;ll help you set up your personalized dashboard in just a few steps.
      </p>
    </div>
  );
}

function DashboardCustomizationStep() {
  const { dashboardWidgets, toggleWidget } = useUserPreferences();

  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
        Select the widgets you want to see on your dashboard:
      </p>

      <div className="grid grid-cols-2 gap-3">
        {dashboardWidgets.map((widget) => (
          <div
            key={widget.id}
            className={`
              p-4 border rounded-lg cursor-pointer transition-colors
              ${widget.enabled
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-700'
              }
            `}
            onClick={() => toggleWidget(widget.id, !widget.enabled)}
          >
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={widget.enabled}
                onChange={() => { }}
                className="w-4 h-4 text-blue-500 rounded focus:ring-blue-500"
              />
              <label className="ml-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                {widget.name}
              </label>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function NotificationPreferencesStep() {
  const [reminders, setReminders] = useState<string[]>(['1-day']);
  const [notificationsEnabled, setNotificationsEnabled] = useState<boolean>(true);

  const reminderOptions = [
    { id: 'same-day', label: 'Same day' },
    { id: '1-day', label: '1 day before' },
    { id: '3-days', label: '3 days before' },
    { id: '1-week', label: '1 week before' },
  ];

  const toggleReminder = (reminderId: string) => {
    setReminders(prev =>
      prev.includes(reminderId)
        ? prev.filter(id => id !== reminderId)
        : [...prev, reminderId]
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Enable bill reminders
        </span>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={notificationsEnabled}
            onChange={() => setNotificationsEnabled(!notificationsEnabled)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-transform dark:border-gray-600 peer-checked:bg-blue-600"></div>
        </label>
      </div>

      {notificationsEnabled && (
        <div className="mt-3">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            When would you like to be reminded?
          </p>
          <div className="space-y-2">
            {reminderOptions.map((option) => (
              <div key={option.id} className="flex items-center">
                <input
                  id={`reminder-${option.id}`}
                  type="checkbox"
                  checked={reminders.includes(option.id)}
                  onChange={() => toggleReminder(option.id)}
                  className="w-4 h-4 text-blue-500 rounded focus:ring-blue-500"
                />
                <label
                  htmlFor={`reminder-${option.id}`}
                  className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

function ThemePreferencesStep() {
  const { isDarkMode, toggleDarkMode } = useDarkMode();
  const { themeColor, setThemeColor } = useThemeColor();
  const { toggleAnimations, showAnimations } = useUserPreferences();

  const themeOptions = [
    { color: 'blue', label: 'Blue' },
    { color: 'purple', label: 'Purple' },
    { color: 'green', label: 'Green' },
    { color: 'orange', label: 'Orange' },
    { color: 'teal', label: 'Teal' },
  ];

  return (
    <div className="space-y-6">
      <div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
          Choose a color theme:
        </p>
        <div className="flex flex-wrap gap-2">
          {themeOptions.map((option) => (
            <button
              key={option.color}
              className={`
                flex flex-col items-center p-2 rounded-lg transition-colors
                ${themeColor === option.color ? 'ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-800' : ''}
              `}
              onClick={() => setThemeColor(option.color as any)}
            >
              <div
                className="w-8 h-8 rounded-full mb-1"
                style={{
                  backgroundColor: `var(--${option.color}-theme)`
                }}
              />
              <span className="text-xs font-medium">{option.label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Dark Mode
          </span>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={isDarkMode}
              onChange={() => toggleDarkMode()}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-transform dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Enable Animations
          </span>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={showAnimations}
              onChange={() => toggleAnimations()}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-transform dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>
    </div>
  );
}

function CompletionStep() {
  return (
    <div className="flex flex-col items-center text-center">
      <div className="w-16 h-16 flex items-center justify-center bg-green-100 dark:bg-green-900/30 rounded-full mb-4">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 text-green-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M5 13l4 4L19 7"
          />
        </svg>
      </div>

      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
        Your dashboard is ready!
      </h3>

      <p className="mt-2 text-gray-600 dark:text-gray-400">
        You can always change your preferences in the settings page.
      </p>
    </div>
  );
}