import { AuthProvider } from "@/components/auth/AuthProvider";
import { AppLayout } from "@/components/layout/AppLayout";
import { ConditionalThemeColor } from "@/components/meta/ConditionalThemeColor";
import { NotificationPermission } from "@/components/pwa/NotificationPermission";
import { PwaIcons } from "@/components/pwa/PwaIcons";
import { reportWebVitals } from '@/lib/vitals';
import { BillsProvider } from "@/stores/billsStore";
import { FinancialProvider } from "@/stores/financialStore";
import { ThemeProvider } from "@/stores/themeStore";
import type { Metadata, Viewport } from "next";
import dynamic from 'next/dynamic';
import { Inter, Roboto_Mono } from "next/font/google";
import Script from "next/script";
import "./globals.css";
const MotionProvider = dynamic(() => import('@/components/layout/MotionProvider'), { ssr: false });
// Defer OneSignal SDK init to client and after hydration
const OneSignalInitializer = dynamic(() => import('@/components/notifications/OneSignalInitializer'), { ssr: false });

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap'
});

const roboto_mono = Roboto_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
  display: 'swap'
});

// Version string used for cache busting of static assets referenced in metadata
const VERSION = process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0';

export const metadata: Metadata = {
  title: "PayDay Pilot",
  description: "Track, manage, and forecast your bills with ease",
  metadataBase: new URL('https://www.paydaypilot.app'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    url: 'https://www.paydaypilot.app/',
    title: 'PayDay Pilot',
    description: 'Track, manage, and forecast your bills with ease',
    siteName: 'PayDay Pilot',
    images: [{ url: `/images/optimized/logo.png?v=${VERSION}`, width: 1200, height: 630 }],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'PayDay Pilot',
    description: 'Track, manage, and forecast your bills with ease',
    images: [`/images/optimized/logo.png?v=${VERSION}`],
  },
  icons: {
    icon: [
      { url: `/favicon.ico?v=${VERSION}`, sizes: "any" },
      { url: `/icons/icon-48x48.png?v=${VERSION}`, sizes: "48x48", type: "image/png" },
      { url: `/icons/icon-96x96.png?v=${VERSION}`, sizes: "96x96", type: "image/png" },
      { url: `/icons/icon-192x192.png?v=${VERSION}`, sizes: "192x192", type: "image/png" },
      { url: `/icons/icon-512x512.png?v=${VERSION}`, sizes: "512x512", type: "image/png" },
      { url: `/icons/icon-512x512.svg?v=${VERSION}`, sizes: "512x512", type: "image/svg+xml" },
    ],
    apple: [
      { url: `/icons/icon-192x192.png?v=${VERSION}`, sizes: "192x192", type: "image/png" },
      { url: `/icons/icon-512x512.png?v=${VERSION}`, sizes: "512x512", type: "image/png" },
    ],
    shortcut: `/favicon.ico?v=${VERSION}`,
  },
  manifest: "/manifest.json",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  // Removed maximumScale and userScalable for better accessibility
  // and to eliminate console warnings
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className={`${inter.variable} ${roboto_mono.variable}`}>
      <head>
        <Script
          id="emergency-error-guard"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                // Enhanced error handler for mobile layout stability
                window.addEventListener('error', function(e) {
                  // Specifically handle custom element errors
                  if (e.error && e.error.message && e.error.message.includes('already been defined')) {
                    console.warn('[Mobile Fix] Custom element conflict prevented:', e.error.message);
                    e.preventDefault(); // Prevent error from disrupting layout
                    return false;
                  }
                  console.warn('Global error caught:', e.error, e.filename, e.lineno);
                  // Don't prevent default for other errors - just log
                });
                
                window.addEventListener('unhandledrejection', function(e) {
                  // Handle promise rejections from custom elements
                  if (e.reason && e.reason.message && e.reason.message.includes('custom element')) {
                    console.warn('[Mobile Fix] Custom element promise rejection handled:', e.reason.message);
                    e.preventDefault();
                    return;
                  }
                  console.warn('Unhandled promise rejection:', e.reason);
                });
                
                // Mobile viewport stability fix
                if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                  console.log('[Mobile Fix] Mobile device detected, applying stability fixes');
                  
                  // Prevent zoom on input focus (which can disrupt layout)
                  document.addEventListener('touchstart', function() {}, {passive: true});
                  
                  // Handle orientation changes that might affect custom elements
                  window.addEventListener('orientationchange', function() {
                    setTimeout(function() {
                      // Force layout recalculation after orientation change
                      document.body.style.display = 'none';
                      document.body.offsetHeight; // Trigger reflow
                      document.body.style.display = '';
                    }, 100);
                  });
                }
                
                // Comprehensive CustomElements protection
                try {
                  if (typeof window !== 'undefined' && window.customElements) {
                    var ce = window.customElements;
                    if (!ce.__comprehensiveGuardApplied) {
                      ce.__comprehensiveGuardApplied = true;
                      
                      // Store original methods
                      var originalDefine = ce.define;
                      var originalGet = ce.get;
                      
                      // Enhanced define method with better error handling
                      ce.define = function(name, constructor, options) {
                        try {
                          // Check if element already exists
                          if (ce.get && ce.get(name)) {
                            console.warn('[CustomElements] Element already defined, skipping:', name);
                            return;
                          }
                          
                          // Validate element name
                          if (!name || typeof name !== 'string' || !name.includes('-')) {
                            console.warn('[CustomElements] Invalid element name:', name);
                            return;
                          }
                          
                          // Call original define method
                          return originalDefine.call(this, name, constructor, options);
                        } catch (err) {
                          // Silently handle DOMException for duplicate definitions
                          if (err.name === 'NotSupportedError' || err.message.includes('already been defined')) {
                            console.warn('[CustomElements] Duplicate registration prevented:', name);
                            return;
                          }
                          // Log other errors but don't throw
                          console.warn('[CustomElements] Definition error handled:', name, err.message);
                        }
                      };
                      
                      // Enhanced get method with safety checks
                      ce.get = function(name) {
                        try {
                          return originalGet.call(this, name);
                        } catch (err) {
                          console.warn('[CustomElements] Get error handled:', name, err.message);
                          return undefined;
                        }
                      };
                      
                      // Prevent WebComponents polyfill conflicts
                      if (window.WebComponents && window.WebComponents.ready) {
                        console.log('[CustomElements] WebComponents polyfill detected, applying compatibility fixes');
                      }
                      
                      // Specific fix for mce-autosize-textarea and common conflicts
                      var conflictingElements = [
                        'mce-autosize-textarea',
                        'vscode-webview',
                        'devtools-element',
                        'extension-element'
                      ];
                      
                      conflictingElements.forEach(function(elementName) {
                        try {
                          if (!ce.get(elementName)) {
                            // Pre-register a minimal placeholder to prevent conflicts
                            var PlaceholderElement = class extends HTMLElement {
                              constructor() { super(); }
                              connectedCallback() {
                                console.log('[CustomElements] Placeholder active for:', elementName);
                              }
                            };
                            ce.define(elementName, PlaceholderElement);
                            console.log('[CustomElements] Preemptive registration for:', elementName);
                          }
                        } catch (err) {
                          // Element might already exist, which is fine
                          console.log('[CustomElements] Element', elementName, 'already registered or failed to register');
                        }
                      });
                    }
                  }
                } catch (e) {
                  console.warn('[CustomElements] Guard setup failed, but continuing:', e.message);
                }
              })();
            `,
          }}
        />
        {/* Charset and content-type for compatibility and accessibility tools */}
        <meta charSet="utf-8" />
        <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />

        {/* Performance: preconnect to critical third-party origins */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="preconnect" href="https://www.google-analytics.com" />
        <link rel="preconnect" href="https://www.googleapis.com" />
        <link rel="preconnect" href="https://firebase.googleapis.com" />
        <link rel="preconnect" href="https://cdn.onesignal.com" />

        <PwaIcons />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        {/* Add the standard mobile-web-app-capable meta tag */}
        <meta name="mobile-web-app-capable" content="yes" />
        {/* Theme color only for browsers that support it (excludes Firefox) */}
        {/* Prevent flash of unstyled content by applying dark mode immediately */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  const savedDarkMode = localStorage.getItem('darkMode');
                  const shouldBeDark = savedDarkMode !== null
                    ? savedDarkMode === 'true'
                    : window.matchMedia('(prefers-color-scheme: dark)').matches;

                  if (shouldBeDark) {
                    document.documentElement.classList.add('dark');
                  }
                } catch (e) {
                  // Fallback to system preference
                  if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.classList.add('dark');
                  }
                }
              })();
            `,
          }}
        />
      </head>
      <body className="min-h-screen bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
        {/* Skip link for keyboard navigation */}
        <a href="#main-content" className="skip-link-enhanced">
          Skip to main content
        </a>
        <Script
          id="custom-elements-duplicate-guard"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  if (typeof window === 'undefined') return;
                  var ce = window.customElements;
                  if (!ce || ce.__duplicateGuardApplied) return;

                  var originalDefine = ce.define.bind(ce);
                  var patched = new Set();

                  ce.__duplicateGuardApplied = true;
                  ce.__originalDefine = originalDefine;
                  ce.__patchedElements = patched;

                  ce.define = function(name, constructor, options) {
                    try {
                      if (typeof ce.get === 'function' && ce.get(name)) {
                        patched.add(name);
                        return;
                      }
                      originalDefine(name, constructor, options);
                    } catch (err) {
                      var message = err && err.message ? err.message : '';
                      if (
                        message.indexOf('has already been defined') !== -1 ||
                        message.indexOf('already been registered') !== -1 ||
                        message.indexOf('already been called') !== -1 ||
                        name === 'mce-autosize-textarea'
                      ) {
                        patched.add(name);
                        return;
                      }
                      throw err;
                    }
                  };
                } catch (error) {
                  console.warn('customElements duplicate guard failed', error);
                }
              })();
            `,
          }}
        />
        {/* Skip link for keyboard navigation accessibility */}
        <a
          href="#main-content"
          className="skip-link"
        >
          Skip to main content
        </a>

        <AuthProvider>
          <ThemeProvider>
            <ConditionalThemeColor color="#3880ff" />
            <OneSignalInitializer /> {/* Added */}
            <BillsProvider>
              <FinancialProvider>
                <MotionProvider>
                  <NotificationPermission reason="Get timely reminders about upcoming bills and important updates.">
                    <AppLayout>{children}</AppLayout>
                  </NotificationPermission>
                </MotionProvider>
              </FinancialProvider>
            </BillsProvider>
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  );
}

// Report Web Vitals to our endpoint (Next.js will call this if exported from root layout in App Router projects)
export { reportWebVitals };

