/**
 * Browser compatibility utilities for handling features that aren't supported in all browsers
 */

// Check if browser supports a specific feature
export const browserSupports = {
    fetchPriority: typeof window !== 'undefined' && 'fetchPriority' in HTMLImageElement.prototype,
    themeColor: typeof window !== 'undefined' && !navigator.userAgent.toLowerCase().includes('firefox'),
    // Add more browser feature checks as needed
};

// Get supported attributes for elements
export const getSupportedAttributes = () => ({
    img: {
        ...(browserSupports.fetchPriority && { fetchPriority: true }),
    },
    link: {
        ...(browserSupports.fetchPriority && { fetchPriority: true }),
    },
    meta: {
        ...(browserSupports.themeColor && { themeColor: true }),
    },
});

// Conditional attribute helper
export const conditionalAttribute = (condition: boolean, attribute: any) =>
    condition ? attribute : undefined;